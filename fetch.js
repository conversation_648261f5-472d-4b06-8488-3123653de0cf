// POST请求模板
async function postRequest(url, data) {
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 如果需要认证，可以添加：
        // 'Authorization': 'Bearer ' + token,
      },
      body: JSON.stringify(data)
    });

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 解析JSON响应
    const result = await response.json()
    return result;
  } catch (error) {
    console.error('POST请求失败:', error);
    throw error;
  }
}

// 使用示例
async function exampleUsage() {
  const apiUrl = 'https://mail.xoxome.online/api/auth/login';
  const postData = {
    username: 'halo',
    password: '12345678aka'
  };
  try {
    const result = await postRequest(apiUrl, postData);
    console.log('请求成功:', result);
  } catch (error) {
    console.error('请求失败:', error);
  }
}

// 导出函数供其他文件使用
// export { postRequest };